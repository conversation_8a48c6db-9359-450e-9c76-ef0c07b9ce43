import Parcel from "single-spa-react/parcel";
import * as Styles from "./styles";
import Carrousel from "../Carrousel";
import { Accordion, Logo } from "@iterpecdev/design-sistem";
import CardHotel from "../CardHotel";
import CardReview from "../CardReview";
import Footer from "../Footer";
import apis from "../../Services";
import { useEffect, useState } from "react";
import moment from "moment";
import {
  getSearch,
  setHeader,
  setSearch,
  getUser,
  Hooks,
  // @ts-ignore
} from "@iterpec/booking-utility";
import { useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next";
import Api from "../../Services";
import { IUser } from "../../../../iterpec-booking-utility/src/store/User/types";
import { IRegionResponse } from "../../Services/types/regionResponse";



export const Dashboard = () => {
  const { useAuth, useIsomorphicLayoutEffect } = Hooks;
  const navigate = useNavigate();
  const user: IUser = getUser();
  const { t } = useTranslation();
  const isMobile = Hooks.useMobileDetect().isMobile();

  const [regions, setRegions] = useState<IRegionResponse[]>([]);

  const [hotelsRecommended, setHotelsRecommended] = useState([]);
  
  const search = getSearch();
  const hotelList = [
    "1d550540-cd19-4102-8943-6dd1c2f525ff",
    "5136c8ba-20ce-46ac-9109-f350be0319fa",
    "2d2ad129-63af-466a-bd7e-c47f139b771a",
  ];
  const getHotelsRecommended = async () => {
    try {
    } catch (error) {
      console.log(error);
    }
  };

  const getClientRegions = async () => {
    try {
      if (user?.clientId) {
        const api = new Api();
        const response = await api.getClientRegions(user.clientId);
        setRegions(response.data);
        console.log('Regions loaded:', response.data);
      }
    } catch (error) {
      console.error('Error loading regions:', error);
    }
  };

  const SelectRegion = (region: IRegionResponse) => {
    setSearch({
      ...search,
      destiny_id: region.regionId,
      destiny: region.name,
      type: region.regionType,
    });
    navigate("/search");
  };
  const DEFAULT_DATE = {
    CHECKIN: moment().add("60", "d").toDate(),
    CHECKOUT: moment().add("65", "d").toDate(),
  };
  useEffect(() => {
    /*     getHotelsRecommended(); */
    getClientRegions();
  }, []);
  useIsomorphicLayoutEffect(() => {
    setTimeout(() => {
      setHeader({
        $showSearch: true,
        $showFallback: false,
        $showMenu: true,
        $show: true
      });
    }, 10);
    setSearch({
      toWork: false,
      quantityRooms: 1,
      checkIn: DEFAULT_DATE.CHECKIN,
      checkOut: DEFAULT_DATE.CHECKOUT,
      rooms: [
        { id: 1, adults: 1, children: 0, childrenAge: [{ age: 0, id: 1 }] },
      ],
    });
  }, []);
  return (
    <>
      <Styles.Container>
        <Styles.Wrapper>
          <div>
            <h2>{t("dashboard.headerDestinations")}</h2>
            <h3>{t("dashboard.headerDestinationsDescription")}</h3>
            <Styles.WrapperDestination>
              {regions?.map((region) => (
                <Carrousel
                  key={region.id}
                  name={region.name}
                  nameDisplay={region.name}
                  description={region.description}
                  image={region.imageUrl}
                  onClick={() => SelectRegion(region)}
                />
              ))}
            </Styles.WrapperDestination>
          </div>
          <div>
            <h2>{t("dashboard.FAQ")}</h2>
            <Styles.WrapperFAQ>
              <Accordion title={`${t("dashboard.titleTravelCanBeSimple")}`}>
                <div
                  dangerouslySetInnerHTML={{
                    __html: `${t("dashboard.contentTravelCanBeSimple")}`,
                  }}
                />
              </Accordion>
              <Accordion title={t("dashboard.titleLivingWithoutSurprises")}>
                <div
                  dangerouslySetInnerHTML={{
                    __html: `${t("dashboard.contentLivingWithoutSurprises")}`,
                  }}
                />
              </Accordion>
            </Styles.WrapperFAQ>
          </div>
        </Styles.Wrapper>
      </Styles.Container>
      <Footer />
    </>
  );
};
